import{d as f,D as a,aa as O,ab as y,ac as d}from"./index-Di7bhak2.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){d(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.5 0V2H15.5V0H17.5V2H21V10.5H19V9H5V21H14V23H3V2H6.5V0H8.5ZM5 7H19V4H5V7ZM18.75 14C17.2312 14 16 15.2312 16 16.75C16 18.0023 16.7347 19.2045 17.6148 20.1722C18.0223 20.6202 18.4324 20.9864 18.75 21.2466C19.0676 20.9864 19.4777 20.6202 19.8852 20.1722C20.7653 19.2045 21.5 18.0023 21.5 16.75C21.5 15.2312 20.2688 14 18.75 14ZM18.75 23.7013C18.4993 23.534 18.2442 23.3723 18.0005 23.1949C17.8856 23.1113 17.7252 22.9906 17.5345 22.836C17.1545 22.5281 16.6463 22.0799 16.1352 21.5179C15.1403 20.4239 14 18.7512 14 16.75C14 14.1266 16.1266 12 18.75 12C21.3734 12 23.5 14.1266 23.5 16.75C23.5 18.7512 22.3597 20.4239 21.3648 21.5179C20.8537 22.0799 20.3455 22.5281 19.9655 22.836C19.7748 22.9906 19.6144 23.1113 19.4995 23.1949C19.2558 23.3723 19.0007 23.534 18.75 23.7013ZM17.5 16H20V18H17.5V16Z"}}]},V=f({name:"TaskLocationIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),p=a(()=>["t-icon","t-icon-task-location",o.value]),u=a(()=>s(s({},c.value),t.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(m,C.value)}});export{V as default};

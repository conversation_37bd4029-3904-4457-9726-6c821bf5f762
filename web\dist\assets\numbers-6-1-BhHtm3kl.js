import{d as C,D as a,aa as O,ab as m,ac as y}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.4207 3.5H14.7297L11.2571 9.55216C11.4998 9.51778 11.7478 9.5 12 9.5C14.8995 9.5 17.25 11.8505 17.25 14.75C17.25 17.6495 14.8995 20 12 20C9.10051 20 6.75 17.6495 6.75 14.75C6.75 13.9812 6.91589 13.2484 7.21478 12.5878C7.34627 12.2972 7.50351 12.0206 7.68362 11.761L12.4207 3.5ZM9.01877 13.4532C8.84617 13.8492 8.75 14.2871 8.75 14.75C8.75 16.5449 10.2051 18 12 18C13.7949 18 15.25 16.5449 15.25 14.75C15.25 12.9551 13.7949 11.5 12 11.5C10.9218 11.5 9.96475 12.0251 9.37295 12.8359L9.01877 13.4532Z"}}]},g=C({name:"Numbers61Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),p=a(()=>["t-icon","t-icon-numbers-6-1",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>m(b,v.value)}});export{g as default};

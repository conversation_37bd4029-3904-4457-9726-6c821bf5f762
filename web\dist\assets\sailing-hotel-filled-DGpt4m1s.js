import{d,D as a,aa as O,ab as y,ac as g}from"./index-Di7bhak2.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.17134 1.01475L7 .811035V6H16.0189C14.4259 3.55866 11.9091 1.66479 8.17134 1.01475zM17.0786 8H7V11H17.9958C17.7893 9.98461 17.4908 8.97227 17.0786 8zM18.2902 13H7V16H18.3594C18.4033 15.036 18.3868 14.0244 18.2902 13zM18.1771 18H7V21H4V23H21V21H17.508C17.7907 20.1416 18.0232 19.115 18.1771 18z"}}]},C=d({name:"SailingHotelFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-sailing-hotel-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(m,f.value)}});export{C as default};

import{d as L,D as a,aa as O,ab as y,ac as d}from"./index-Di7bhak2.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 1.5V6.58579L15.5 4.08579L16.9142 5.5L12 10.4142L7.08579 5.5L8.5 4.08579L11 6.58579L11 1.5L13 1.5ZM21 13L3 13L3 11L21 11L21 13ZM12 13.5858L16.9142 18.5L15.5 19.9142L13 17.4142L13 22.5H11L11 17.4142L8.5 19.9142L7.08579 18.5L12 13.5858Z"}}]},g=L({name:"ShrinkVerticalIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:s}=O(t),p=a(()=>["t-icon","t-icon-shrink-vertical",i.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};

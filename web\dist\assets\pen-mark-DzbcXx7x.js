import{d as O,D as a,aa as y,ab as L,ac as d}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23.5548 6.87928L17.3278 0.652344L3.97906 14.0011L3.41366 16.8281L7.37901 20.7935L10.206 20.2281L23.5548 6.87928ZM20.7263 6.87928L9.21997 18.3857L8.03636 18.6224L5.58474 16.1708L5.82146 14.9871L17.3278 3.48077L20.7263 6.87928ZM1.38574 19.6129L4.59423 22.8214L6.00844 21.4072L2.79996 18.1987L1.38574 19.6129Z"}}]},g=O({name:"PenMarkIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-pen-mark",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>L(m,v.value)}});export{g as default};

import{d,D as a,aa as O,ab as y,ac as m}from"./index-Di7bhak2.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var H={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.0004 2V7H9.00041V2H11.0004ZM7.00041 3V7H5.00041V3H7.00041ZM15.0004 3V7H13.0004V3H15.0004ZM2.92676 8H21.0004V13C21.0004 15.2091 19.2095 17 17.0004 17H16.0669C15.5284 18.2443 14.6199 19.2901 13.4843 20H20.0004V22H3.00041V20H6.50591C4.79741 18.9327 3.60555 17.1027 3.422 14.9613L3.42138 14.9541L2.92676 8ZM16.5754 15H17.0004C18.105 15 19.0004 14.1046 19.0004 13V10H16.9318L16.5794 14.9541L16.5788 14.9613C16.5777 14.9742 16.5766 14.9871 16.5754 15Z"}}]},g=d({name:"TeaFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-tea-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(H,f.value)}});export{g as default};

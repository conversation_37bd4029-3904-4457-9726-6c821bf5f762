import{d,D as a,aa as m,ab as O,ac as y}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.0001 3V11L18.5859 11L16.5859 9L18.0002 7.58579L22.4144 12L18.0002 16.4142L16.5859 15L18.5859 13L13.0001 13V21H11.0001V13L5.41436 13L7.41436 15L6.00015 16.4142L1.58594 12L6.00015 7.58579L7.41436 9L5.41436 11L11.0001 11V3H13.0001Z"}}]},b=d({name:"OrderAdjustmentColumnIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=m(t),u=a(()=>["t-icon","t-icon-order-adjustment-column",o.value]),p=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:u.value,style:p.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(L,v.value)}});export{b as default};

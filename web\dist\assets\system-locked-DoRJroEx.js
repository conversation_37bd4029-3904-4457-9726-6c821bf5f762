import{d as y,D as a,aa as d,ab as O,ac as m}from"./index-Di7bhak2.js";function s(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?s(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 1H23V11H21V3H3V16H13V18H1V1ZM19.5015 14.5C20.1918 14.5 20.7515 15.0596 20.7515 15.75V16.5H18.2515V15.75C18.2515 15.0596 18.8111 14.5 19.5015 14.5ZM22.7515 16.5V15.75C22.7515 13.9551 21.2964 12.5 19.5015 12.5C17.7065 12.5 16.2515 13.9551 16.2515 15.75V16.5H15V23H24V16.5H22.7515ZM22 18.5V21H17V18.5H22ZM2.25 21H13V23H2.25V21Z"}}]},b=y({name:"SystemLockedIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=d(r),p=a(()=>["t-icon","t-icon-system-locked",o.value]),u=a(()=>i(i({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(V,v.value)}});export{b as default};

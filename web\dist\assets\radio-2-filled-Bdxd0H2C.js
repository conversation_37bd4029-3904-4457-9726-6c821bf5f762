import{d,D as a,aa as O,ab as y,ac as C}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 11C13 9.89543 13.8954 9 15 9C16.1046 9 17 9.89543 17 11C17 12.1046 16.1046 13 15 13C13.8954 13 13 12.1046 13 11Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M23 3V19H19V22H17V19H7V22H5V19H1V3H23ZM15 7C12.7909 7 11 8.79086 11 11C11 13.2091 12.7909 15 15 15C17.2091 15 19 13.2091 19 11C19 8.79086 17.2091 7 15 7ZM9 8H5V10H9V8ZM9 12H5V14H9V12Z"}}]},b=d({name:"Radio2FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-radio-2-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(g,f.value)}});export{b as default};

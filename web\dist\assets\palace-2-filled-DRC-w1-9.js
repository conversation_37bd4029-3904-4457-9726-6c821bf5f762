import{d,D as n,aa as C,ab as O,ac as y}from"./index-Di7bhak2.js";function i(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.0039 2.00195L13.0018 3.08326C15.8386 3.56007 18 6.02763 18 9L20 9V3H22V22H16.5V19.4999C16.5 17.5577 15.5303 16.0975 14.7299 15.217C14.319 14.765 13.9124 14.4199 13.6075 14.1861C13.4536 14.0681 13.3215 13.9754 13.2223 13.9088L13.029 13.7849L12 13.1675L10.971 13.7849L10.7777 13.9088C10.6785 13.9754 10.5464 14.0681 10.3925 14.1861C10.0876 14.4199 9.68105 14.765 9.27012 15.217C8.4697 16.0975 7.5 17.5577 7.5 19.4999V22H2V3H4V9L6 9C6 6.02638 8.1632 3.558 11.0018 3.08266L11.0039 1.99805L13.0039 2.00195ZM10.998 5.99805V8.00195H13.002V5.99805H10.998Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12 15.5C12 15.5 9.5 17 9.5 19.5V22H14.5V19.5C14.5 17 12 15.5 12 15.5Z"}}]},b=d({name:"Palace2FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,a){var{attrs:t}=a,r=n(()=>e.size),{className:l,style:s}=C(r),p=n(()=>["t-icon","t-icon-palace-2-filled",l.value]),u=n(()=>c(c({},s.value),t.style)),f=n(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};

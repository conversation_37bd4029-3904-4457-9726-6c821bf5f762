import{d as C,D as a,aa as d,ab as O,ac as y}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.5 7C3.5 4.23858 5.73858 2 8.5 2C11.2614 2 13.5 4.23858 13.5 7C13.5 9.76142 11.2614 12 8.5 12C5.73858 12 3.5 9.76142 3.5 7ZM20.5725 5.64806L21.0704 6.5153C21.6797 7.57662 22.0001 8.77914 21.9996 10.0029C21.9992 11.2267 21.678 12.429 21.0679 13.4899L20.5694 14.3568L18.8356 13.3598L19.3341 12.4929C19.7699 11.7351 19.9993 10.8764 19.9996 10.0022C20 9.12809 19.7711 8.26915 19.3359 7.51107L18.838 6.64382L20.5725 5.64806ZM17.5375 7.39046L18.0354 8.25771C18.34 8.78837 18.5002 9.38962 18.5 10.0015C18.4998 10.6134 18.3392 11.2146 18.0341 11.745L17.5356 12.6119L15.8019 11.6149L16.3003 10.748C16.4311 10.5207 16.4999 10.2631 16.5 10.0008C16.5001 9.73858 16.4314 9.4809 16.3009 9.25347L15.803 8.38623L17.5375 7.39046ZM0 19C0 16.2386 2.23858 14 5 14H12C14.7614 14 17 16.2386 17 19V21C9.78733 21 3.91775 21 0 21V19Z"}}]},g=C({name:"UserTalk1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-user-talk-1-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};

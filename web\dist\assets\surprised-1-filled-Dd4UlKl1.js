import{d,D as a,aa as C,ab as O,ac as y}from"./index-Di7bhak2.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11 15.5C11 14.4518 11.6444 14 12 14C12.3556 14 13 14.4518 13 15.5C13 16.5482 12.3556 17 12 17C11.6444 17 11 16.5482 11 15.5Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23ZM9 12V8H7V12H9ZM17 12V8H15V12H17ZM12 12C10.1464 12 9 13.7868 9 15.5C9 17.2132 10.1464 19 12 19C13.8536 19 15 17.2132 15 15.5C15 13.7868 13.8536 12 12 12Z"}}]},b=d({name:"Surprised1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=C(t),p=a(()=>["t-icon","t-icon-surprised-1-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(g,f.value)}});export{b as default};

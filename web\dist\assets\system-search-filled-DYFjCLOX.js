import{d as C,D as a,aa as y,ab as d,ac as O}from"./index-Di7bhak2.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 2H1V18H11.5412C11.514 17.7537 11.5 17.5035 11.5 17.25 11.5 16.071 11.8023 14.9626 12.3336 13.998H3V3.99805H21V11.0837C21.7486 11.4181 22.4252 11.8849 23 12.4542V2zM3 20H12.0837C12.4181 20.7486 12.8849 21.4252 13.4542 22H3V20z"}},{tag:"path",attrs:{fill:"currentColor",d:"M23 17.25C23 18.1992 22.7216 19.0834 22.2419 19.8254L23.9142 21.5017L22.4983 22.9142L20.8282 21.2401C20.0857 21.7209 19.2004 22 18.25 22C15.6266 22 13.5 19.8734 13.5 17.25C13.5 14.6266 15.6266 12.5 18.25 12.5C20.8734 12.5 23 14.6266 23 17.25ZM21 17.25C21 15.7312 19.7688 14.5 18.25 14.5C16.7312 14.5 15.5 15.7312 15.5 17.25C15.5 18.7688 16.7312 20 18.25 20C19.006 20 19.6907 19.695 20.1878 19.2013L20.1965 19.1926C20.693 18.6951 21 18.0084 21 17.25Z"}}]},h=C({name:"SystemSearchFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=y(r),p=a(()=>["t-icon","t-icon-system-search-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>d(m,f.value)}});export{h as default};

import{d,D as a,aa as O,ab as y,ac as C}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function p(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4.96077 3.54632L9.61383 8.19938C11.0678 7.28457 12.9326 7.28457 14.3866 8.19938L19.0394 3.5465C14.9717.151228 9.02856.151168 4.96077 3.54632zM20.4536 4.96072L15.8008 9.61359C16.7156 11.0676 16.7156 12.9323 15.8008 14.3863L20.4537 19.0392C23.8488 14.9715 23.8488 9.02844 20.4536 4.96072zM19.0395 20.4535L14.3866 15.8005C12.9326 16.7153 11.0678 16.7153 9.61383 15.8005L4.96072 20.4536C9.02853 23.8488 14.9718 23.8488 19.0395 20.4535zM3.5465 19.0394L8.19962 14.3863C7.28482 12.9323 7.28482 11.0676 8.19962 9.61359L3.54654 4.96051C.151168 9.0283.151155 14.9716 3.5465 19.0394z"}}]},g=d({name:"SupportFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=O(t),c=a(()=>["t-icon","t-icon-support-filled",l.value]),u=a(()=>p(p({},s.value),r.style)),f=a(()=>({class:c.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{g as default};

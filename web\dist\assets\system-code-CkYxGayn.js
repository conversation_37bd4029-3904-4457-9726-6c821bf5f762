import{d as y,D as a,aa as d,ab as O,ac as m}from"./index-Di7bhak2.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function i(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 2H23V18H1V2ZM3 4V16H21V4H3ZM15 6.08579L18.9142 10L15 13.9142L13.5858 12.5L16.0858 10L13.5858 7.5L15 6.08579ZM10.4142 7.50001L7.91422 10L10.4142 12.5L9 13.9142L5.08579 10L9 6.08579L10.4142 7.50001ZM3 20H21V22H3V20Z"}}]},L=y({name:"SystemCodeIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=d(r),p=a(()=>["t-icon","t-icon-system-code",o.value]),u=a(()=>i(i({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:f})}}));return()=>O(b,v.value)}});export{L as default};

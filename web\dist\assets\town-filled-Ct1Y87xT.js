import{d,D as a,aa as H,ab as O,ac as V}from"./index-Di7bhak2.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){V(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.00391 7.99805H6V10.002H8.00391V7.99805zM8.00391 11.998H6V14.002H8.00391V11.998zM8.00391 15.998H6V18.002H8.00391V15.998z"}},{tag:"path",attrs:{fill:"currentColor",d:"M7.44237 2H2V22H22V8.63795L16.4684 2H11V5.91339L7.44237 2ZM11 8.88661V20H4V4H6.55763L11 8.88661ZM15 7.99805H17.0039V10.002H15V7.99805ZM15 11.998H17.0039V14.002H15V11.998ZM17.0039 15.998V18.002H15V15.998H17.0039Z"}}]},m=d({name:"TownFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=H(r),p=a(()=>["t-icon","t-icon-town-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(y,f.value)}});export{m as default};

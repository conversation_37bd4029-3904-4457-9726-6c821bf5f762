import{d,D as a,aa as y,ab as O,ac as C}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 3C7.02944 3 3 7.02945 3 12C3 16.9706 7.02944 21.0001 12 21.0001H13V23.0001H12C5.92486 23.0001 1 18.0752 1 12C1 5.92489 5.92486 1 12 1C18.0751 1 23 5.92489 23 12V13H21V12C21 7.02945 16.9706 3 12 3ZM9.5 7.13148L16.8028 12L9.5 16.8685V7.13148ZM11.5 10.8685V13.1315L13.1972 12L11.5 10.8685ZM20 15V18H23V20H20V23H18V20H15V18H18V15H20Z"}}]},g=d({name:"PlayCircleStrokeAddIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=y(t),p=a(()=>["t-icon","t-icon-play-circle-stroke-add",l.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,v.value)}});export{g as default};

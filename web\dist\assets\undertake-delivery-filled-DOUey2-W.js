import{d as f,D as a,aa as d,ab as y,ac as O}from"./index-Di7bhak2.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function C(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M21.5 2H7.5V9.84233C7.80877 9.78054 8.12366 9.74947 8.44006 9.75001H13.125C14.2853 9.75001 15.3981 10.2109 16.2186 11.0314C16.6621 11.4749 17.0006 12.0039 17.218 12.5796L19.1823 12.1278C19.7653 11.9779 20.3747 11.9595 20.9662 12.0743C21.1477 12.1096 21.326 12.1571 21.5 12.2163V2ZM12.5 4H16.5V8L14.5 6.5L12.5 8V4Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M19.5394 13.5848L15.9866 14.402C15.9955 14.3103 16 14.2179 16 14.125C16 13.3625 15.6971 12.6312 15.1579 12.0921C14.6188 11.5529 13.8875 11.25 13.125 11.25H8.43868C8.01192 11.2491 7.58917 11.3323 7.19455 11.4948C6.79953 11.6574 6.44046 11.8964 6.13787 12.1979L4.08579 14.25H0V21.5H11.3731L17.5695 19.9509L21.3093 18.3556L21.336 18.3422C21.761 18.13 22.1185 17.8036 22.3682 17.3996C22.618 16.9956 22.7502 16.5292 22.75 16.0542C22.7495 15.6609 22.6584 15.2731 22.4837 14.9209C22.309 14.5686 22.0554 14.2614 21.7426 14.0231C21.4298 13.7848 21.0663 13.6218 20.6803 13.5468C20.3019 13.4734 19.9119 13.4864 19.5394 13.5848ZM7.95605 13.3442C8.10798 13.2816 8.27075 13.2496 8.43505 13.25L13.125 13.25C13.3571 13.25 13.5796 13.3422 13.7437 13.5063C13.9078 13.6704 14 13.8929 14 14.125C14 14.3571 13.9078 14.5796 13.7437 14.7437C13.5796 14.9078 13.3571 15 13.125 15H10V17L13.6135 17L20.0256 15.5252L20.0455 15.5198C20.1282 15.4971 20.2149 15.4938 20.299 15.5102C20.3831 15.5265 20.4623 15.562 20.5305 15.6139C20.5986 15.6659 20.6539 15.7328 20.692 15.8096C20.73 15.8863 20.7499 15.9708 20.75 16.0565C20.7499 16.1594 20.7212 16.2604 20.667 16.348C20.6167 16.4294 20.5462 16.4963 20.4625 16.5424L16.9305 18.0491L11.1269 19.5H5.5V15.6642L7.55011 13.6141C7.66639 13.4984 7.80432 13.4066 7.95605 13.3442ZM2 16.25H3.5V19.5H2V16.25Z"}}]},m=f({name:"UndertakeDeliveryFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),c=a(()=>["t-icon","t-icon-undertake-delivery-filled",l.value]),p=a(()=>C(C({},s.value),r.style)),u=a(()=>({class:c.value,style:p.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(L,u.value)}});export{m as default};

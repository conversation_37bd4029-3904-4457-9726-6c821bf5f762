import{d,D as a,aa as C,ab as O,ac as y}from"./index-Di7bhak2.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var V={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 1C9.36545 1 7.2495 1.52703 5.78326 2.06021C5.0502 2.32677 4.47862 2.59519 4.08431 2.80092C3.88708 2.90382 3.73395 2.99117 3.62687 3.05496C3.57332 3.08686 3.53125 3.11289 3.50092 3.13205C3.48575 3.14163 3.47351 3.14949 3.46423 3.15552L3.45254 3.16316L3.44836 3.16592L3.44669 3.16702L3 3.46482V20H2V22H7V9H9V11H11V9H13V22H22V20H21V3.46482L20.5533 3.16702L20.5516 3.16592L20.5475 3.16316L20.5358 3.15552C20.5265 3.14949 20.5142 3.14163 20.4991 3.13205C20.4687 3.11289 20.4267 3.08686 20.3731 3.05496C20.2661 2.99117 20.1129 2.90382 19.9157 2.80092C19.5214 2.59519 18.9498 2.32677 18.2167 2.06021C16.7505 1.52703 14.6345 1 12 1Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M11 22V20H9V22H11zM11 18H9V16.5H11V18zM11 14.5H9V13H11V14.5z"}}]},m=d({name:"PatioFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=C(r),p=a(()=>["t-icon","t-icon-patio-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(V,f.value)}});export{m as default};

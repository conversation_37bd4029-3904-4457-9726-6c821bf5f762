import{d as C,D as a,aa as d,ab as O,ac as y}from"./index-Di7bhak2.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22.5 2V18H6.87574L1.5 22.7038V2H22.5ZM11 8.5C11 7.94771 11.4477 7.5 12 7.5C12.5523 7.5 13 7.94771 13 8.5C13 8.82392 12.9223 9.00753 12.8501 9.11822C12.7729 9.23684 12.6597 9.3355 12.5062 9.42305C12.3462 9.51433 12.1775 9.57513 11.9869 9.64144L11.9686 9.64778C11.8935 9.67378 11.7666 9.71774 11.6602 9.76828C11.6042 9.79486 11.4836 9.85498 11.3644 9.96014C11.2481 10.0627 11.0034 10.3263 11.0034 10.75V11.75H13.0034V11.3975C13.1536 11.3366 13.3243 11.2589 13.4972 11.1603C13.8429 10.9631 14.2288 10.6659 14.5259 10.2099C14.8282 9.74594 15 9.17608 15 8.5C15 6.84314 13.6569 5.5 12 5.5C10.3431 5.5 9 6.84314 9 8.5V9.5H11V8.5ZM13.0039 12.5H11V14.5039H13.0039V12.5Z"}}]},g=C({name:"QuestionnaireFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=d(t),p=a(()=>["t-icon","t-icon-questionnaire-filled",i.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};

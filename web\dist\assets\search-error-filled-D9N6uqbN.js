import{d,D as a,aa as O,ab as y,ac as C}from"./index-Di7bhak2.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15.762 17.1765C14.3137 18.3208 12.579 18.9266 10.8275 18.9937C10.9403 18.5141 11 18.014 11 17.5C11 13.9101 8.08985 11 4.5 11C3.63546 11 2.81034 11.1688 2.05579 11.4752C1.77014 8.9875 2.58141 6.39778 4.48959 4.48959C7.80905 1.17014 13.191 1.17014 16.5104 4.48959C19.5907 7.56987 19.8126 12.4261 17.1763 15.7623L22.5206 21.1067L21.1064 22.5209L15.762 17.1765Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M8.74261 14.6717L7.32839 13.2574L4.49996 16.0859L1.67154 13.2574L0.257324 14.6717L3.08575 17.5001L0.257324 20.3285L1.67154 21.7427L4.49996 18.9143L7.32839 21.7427L8.74261 20.3285L5.91418 17.5001L8.74261 14.6717Z"}}]},h=d({name:"SearchErrorFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=O(t),p=a(()=>["t-icon","t-icon-search-error-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(L,f.value)}});export{h as default};

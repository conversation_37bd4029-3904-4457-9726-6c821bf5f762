import{d,D as a,aa as C,ab as O,ac as y}from"./index-Di7bhak2.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 4C8.85483 4 6.17033 5.21134 5.49292 7.71396C5.27866 8.5055 5.13191 9.29497 5.05728 10L18.9427 10C18.8682 9.29614 18.7218 8.50811 18.5081 7.71785C17.8306 5.21232 15.1471 4 12 4ZM3.04776 10C3.12762 9.11406 3.30525 8.14137 3.56239 7.1914C4.60455 3.34126 8.5638 2 12 2C15.4394 2 19.397 3.3431 20.4388 7.19579C20.6953 8.14435 20.8725 9.11541 20.9522 10H22V12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12V10H3.04776ZM10.998 4.99805H13.002V7.00195H10.998V4.99805ZM7.99805 6.99805H10.002V9.00195H7.99805V6.99805ZM13.998 6.99805H16.002V9.00195H13.998V6.99805Z"}}]},g=d({name:"RiceFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=C(t),p=a(()=>["t-icon","t-icon-rice-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(m,f.value)}});export{g as default};

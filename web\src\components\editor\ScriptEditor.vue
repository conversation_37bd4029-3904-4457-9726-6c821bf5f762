<template>
  <div class="script-editor-container">
    <!-- 脚本编辑器 -->
    <editor
      ref="editorRef"
      v-model:value="scriptValue"
      :language="language"
      :enable-intellisense="enableIntellisense"
      :current-variables="currentVariables"
      :local-variables="localVariables"
      :global-variables="globalVariables"
      :functions="functions"
      :style="editorStyle"
    />

    <!-- 右下角悬浮测试按钮 -->
    <div class="floating-test-button">
      <t-button
        theme="primary"
        size="large"
        shape="circle"
        @click="onClickTest"
        :disabled="!scriptValue?.trim()"
        title="测试脚本"
      >
        <template #icon>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        </template>
      </t-button>
    </div>
  </div>

  <!-- 脚本测试对话框 -->
  <t-dialog
    v-model:visible="scriptTestVisible"
    attach="body"
    header="脚本测试"
    width="70%"
    height="70vh"
    top="15vh"
    :footer="false"
  >
    <div class="script-test-container">
      <div class="test-script-section">
        <div class="section-title">测试脚本</div>
        <div class="script-editor">
          <editor
            v-model:value="scriptTestCode"
            language="javascript"
            style="height: 180px"
            placeholder="请编辑测试脚本代码"
          />
        </div>
      </div>

      <div class="test-content-wrapper">
        <div class="test-input-section">
          <div class="section-title">
            输入参数
            <t-button
              theme="default"
              size="small"
              variant="text"
              @click="refreshInputParameters"
              :loading="refreshingParams"
              style="margin-left: 8px"
            >
              <template #icon>
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path
                    d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
                  />
                </svg>
              </template>
            </t-button>
          </div>
          <div class="input-editor">
            <editor
              v-model:value="scriptTestInputDataJson"
              language="json"
              style="height: 100%"
              placeholder="请编辑测试输入数据（JSON格式）"
            />
          </div>
        </div>

        <div class="test-result-section">
          <div class="section-title">测试结果</div>
          <div v-if="scriptTestResult" class="result-content">
            <div v-if="scriptTestResult.success !== false" class="success-result">
              <div class="result-label">执行成功</div>
              <code-preview :code="scriptTestResult.result?.toString() || 'null'" lang="json" />
              <div v-if="scriptTestResult.executionTimeMs" class="execution-time">
                执行时间: {{ scriptTestResult.executionTimeMs }}ms
              </div>
            </div>
            <div v-else class="error-result">
              <div class="result-label">执行失败</div>
              <div class="error-message">{{ scriptTestResult.errorMessage }}</div>
              <div v-if="scriptTestResult.executionTimeMs" class="execution-time">
                执行时间: {{ scriptTestResult.executionTimeMs }}ms
              </div>
            </div>
          </div>
          <div v-else class="no-result-placeholder">
            <div class="placeholder-text">点击"执行测试"查看结果</div>
          </div>
        </div>
      </div>

      <div class="test-actions">
        <t-button theme="primary" @click="executeScriptTest" :loading="scriptTestLoading"> 执行测试 </t-button>
        <t-button theme="default" @click="saveScriptToEditor"> 保存脚本 </t-button>
        <t-button theme="default" @click="scriptTestVisible = false"> 关闭 </t-button>
      </div>
    </div>
  </t-dialog>
</template>

<script lang="ts">
export default {
  name: 'ScriptEditor',
};
</script>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';

import Editor from './index.vue';
import CodePreview from '@/components/code-preview/index.vue';
import { api, Services } from '@/api/system';

interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    language?: string;
    enableIntellisense?: boolean;
    currentVariables?: VariableData[];
    localVariables?: VariableData[];
    globalVariables?: VariableData[];
    functions?: FunctionData[];
    editorStyle?: string | object;
  }>(),
  {
    modelValue: '',
    language: 'typescript',
    enableIntellisense: true,
    currentVariables: () => [],
    localVariables: () => [],
    globalVariables: () => [],
    functions: () => [],
    editorStyle: 'height: 250px',
  },
);

const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

// 编辑器引用
const editorRef = ref();

// 脚本值的双向绑定
const scriptValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value),
});

// 脚本测试相关状态
const scriptTestVisible = ref(false);
const scriptTestLoading = ref(false);
const scriptTestResult = ref<any>(null);
const scriptTestInputData = ref<any>({});
const scriptTestCode = ref('');
const refreshingParams = ref(false);

// 脚本测试输入数据的JSON字符串表示
const scriptTestInputDataJson = computed({
  get: () => JSON.stringify(scriptTestInputData.value, null, 2),
  set: (value: string) => {
    try {
      scriptTestInputData.value = JSON.parse(value);
    } catch (error) {
      console.warn('Invalid JSON input:', error);
    }
  },
});

// 点击测试按钮
const onClickTest = () => {
  if (!scriptValue.value?.trim()) {
    MessagePlugin.warning('请先输入脚本内容');
    return;
  }

  // 初始化测试脚本代码 - 使用当前编辑器中的脚本内容
  scriptTestCode.value = scriptValue.value;

  // 先显示对话框
  scriptTestVisible.value = true;

  // 在下一个tick中提取参数，确保对话框已经显示
  nextTick(() => {
    // 自动提取脚本中的变量作为入参（初始化）
    extractScriptParameters(true);
  });
};

// 提取脚本参数
const extractScriptParameters = (isInitial: boolean = false) => {
  const script = scriptTestCode.value || '';
  const inputData = {};

  // 分析脚本中使用的变量
  const usedVariables = analyzeScriptVariables(script);

  // 根据分析结果构建输入数据
  usedVariables.forEach((varPath: string) => {
    // 查找对应的变量定义
    const variable = findVariableByPath(varPath);
    console.log('处理变量:', { varPath, variable });
    if (variable) {
      const sampleValue = getSampleValue(variable.type, variable.children, isInitial);
      console.log('生成的示例值:', { varPath, type: variable.type, sampleValue, isInitial });

      // 处理ROOT节点：如果路径包含ROOT，则剔除ROOT节点，子节点往上移
      const processedPath = removeRootFromPath(varPath);
      const processedValue = removeRootFromValue(sampleValue, variable);

      setNestedValue(inputData, processedPath, processedValue);
    } else {
      console.log('未找到变量定义:', varPath);
    }
  });

  scriptTestInputData.value = inputData;
};

// 分析脚本中使用的变量
const analyzeScriptVariables = (script: string) => {
  const usedVariables = new Set();

  console.log('分析脚本:', script);
  console.log(
    '可用的局部变量:',
    props.localVariables.map((v) => v.path),
  );
  console.log(
    '可用的临时变量:',
    props.currentVariables.map((v) => v.key),
  );

  // 移除注释，但保留字符串中的变量引用
  let cleanScript = script
    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
    .replace(/\/\/.*$/gm, ''); // 移除单行注释

  // 先处理模板字符串中的变量引用
  const templateStringPattern = /`([^`]*)`/g;
  let templateMatch: RegExpExecArray | null;
  while ((templateMatch = templateStringPattern.exec(script)) !== null) {
    const templateContent = templateMatch[1];
    console.log('模板字符串内容:', templateContent);
    // 在模板字符串中查找 ${} 表达式
    const expressionPattern = /\$\{([^}]+)\}/g;
    let exprMatch: RegExpExecArray | null;
    while ((exprMatch = expressionPattern.exec(templateContent)) !== null) {
      const expression = exprMatch[1];
      console.log('模板表达式:', expression);
      // 递归分析表达式中的变量
      const exprVariables = analyzeExpression(expression);
      exprVariables.forEach((v: string) => usedVariables.add(v));
    }
  }

  // 移除字符串字面量
  cleanScript = cleanScript
    .replace(/"[^"]*"/g, '""') // 移除双引号字符串
    .replace(/'[^']*'/g, "''") // 移除单引号字符串
    .replace(/`[^`]*`/g, '``'); // 移除模板字符串

  console.log('清理后的脚本:', cleanScript);

  // 匹配局部变量（通过_data访问）- 优先处理，因为更具体
  const localVarPattern = /_data\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
  let match: RegExpExecArray | null;

  while ((match = localVarPattern.exec(cleanScript)) !== null) {
    const varPath = match[1];
    console.log('找到局部变量路径:', varPath);

    // 检查是否是局部变量或其子路径
    const matchingVariable = props.localVariables.find(
      (v) => v.path && (v.path === varPath || varPath.startsWith(v.path + '.') || v.path.startsWith(varPath + '.')),
    );

    if (matchingVariable) {
      usedVariables.add('_data.' + varPath);
      console.log('添加局部变量:', '_data.' + varPath);
    } else {
      console.log('未找到匹配的局部变量定义:', varPath);
    }
  }

  // 匹配临时变量（直接访问）
  const currentVarPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b/g;

  while ((match = currentVarPattern.exec(cleanScript)) !== null) {
    const varPath = match[1];

    // 排除JavaScript关键字和内置对象
    if (!isJavaScriptKeyword(varPath) && !isBuiltinObject(varPath)) {
      console.log('检查临时变量:', varPath);

      // 检查是否是临时变量或其子路径
      // 统一使用 key 字段进行匹配，并处理方法调用的情况
      const matchingVariable = props.currentVariables.find((v) => {
        if (!v.key) return false;

        // 直接匹配变量名
        if (v.key === varPath) return true;

        // 检查是否是变量的属性访问（如 result.property）
        if (varPath.startsWith(v.key + '.')) return true;

        // 检查是否是变量的方法调用（如 result.forEach）
        // 提取变量名部分（方法调用前的部分）
        const varNamePart = varPath.split('.')[0];
        if (v.key === varNamePart) {
          return true; // 找到匹配的变量
        }

        return false;
      });

      if (matchingVariable) {
        // 检查是否是方法调用
        const varNamePart = varPath.split('.')[0];
        if (matchingVariable.key === varNamePart && varPath.includes('.')) {
          const methodPart = varPath.split('.')[1];
          // 检查是否是常见的数组/对象方法
          const commonMethods = [
            'forEach',
            'map',
            'filter',
            'reduce',
            'find',
            'findIndex',
            'some',
            'every',
            'push',
            'pop',
            'shift',
            'unshift',
            'slice',
            'splice',
            'join',
            'concat',
            'indexOf',
            'lastIndexOf',
            'includes',
            'sort',
            'reverse',
            'length',
            'toString',
            'valueOf',
            'hasOwnProperty',
            'propertyIsEnumerable',
          ];

          if (commonMethods.includes(methodPart)) {
            // 如果是方法调用，只添加变量名部分，不包含方法名
            usedVariables.add(matchingVariable.key);
            console.log('添加临时变量（方法调用）:', matchingVariable.key);
          } else {
            // 属性访问，添加完整路径
            usedVariables.add(varPath);
            console.log('添加临时变量（属性访问）:', varPath);
          }
        } else {
          // 直接匹配
          usedVariables.add(varPath);
          console.log('添加临时变量:', varPath);
        }
      }
    }
  }

  console.log('最终提取的变量:', Array.from(usedVariables));
  return Array.from(usedVariables);
};

// 分析表达式中的变量
const analyzeExpression = (expression: string): string[] => {
  const variables: string[] = [];

  // 匹配局部变量（通过_data访问）
  const localVarPattern = /_data\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
  let match: RegExpExecArray | null;

  while ((match = localVarPattern.exec(expression)) !== null) {
    const varPath = match[1];
    const matchingVariable = props.localVariables.find(
      (v) => v.path && (v.path === varPath || varPath.startsWith(v.path + '.') || v.path.startsWith(varPath + '.')),
    );

    if (matchingVariable) {
      variables.push('_data.' + varPath);
    }
  }

  // 匹配临时变量（直接访问）
  const currentVarPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b/g;

  while ((match = currentVarPattern.exec(expression)) !== null) {
    const varPath = match[1];

    if (!isJavaScriptKeyword(varPath) && !isBuiltinObject(varPath)) {
      const matchingVariable = props.currentVariables.find((v) => {
        if (!v.key) return false;

        // 直接匹配变量名
        if (v.key === varPath) return true;

        // 检查是否是变量的属性访问（如 result.property）
        if (varPath.startsWith(v.key + '.')) return true;

        // 检查是否是变量的方法调用（如 result.forEach）
        // 提取变量名部分（方法调用前的部分）
        const varNamePart = varPath.split('.')[0];
        if (v.key === varNamePart) {
          return true; // 找到匹配的变量
        }

        return false;
      });

      if (matchingVariable) {
        // 检查是否是方法调用
        const varNamePart = varPath.split('.')[0];
        if (matchingVariable.key === varNamePart && varPath.includes('.')) {
          const methodPart = varPath.split('.')[1];
          // 检查是否是常见的数组/对象方法
          const commonMethods = [
            'forEach',
            'map',
            'filter',
            'reduce',
            'find',
            'findIndex',
            'some',
            'every',
            'push',
            'pop',
            'shift',
            'unshift',
            'slice',
            'splice',
            'join',
            'concat',
            'indexOf',
            'lastIndexOf',
            'includes',
            'sort',
            'reverse',
            'length',
            'toString',
            'valueOf',
            'hasOwnProperty',
            'propertyIsEnumerable',
          ];

          if (commonMethods.includes(methodPart)) {
            // 如果是方法调用，只添加变量名部分，不包含方法名
            variables.push(matchingVariable.key);
          } else {
            // 属性访问，添加完整路径
            variables.push(varPath);
          }
        } else {
          // 直接匹配
          variables.push(varPath);
        }
      }
    }
  }

  return variables;
};

// 检查是否是JavaScript关键字
const isJavaScriptKeyword = (word: string) => {
  const keywords = [
    'break',
    'case',
    'catch',
    'class',
    'const',
    'continue',
    'debugger',
    'default',
    'delete',
    'do',
    'else',
    'export',
    'extends',
    'finally',
    'for',
    'function',
    'if',
    'import',
    'in',
    'instanceof',
    'let',
    'new',
    'return',
    'super',
    'switch',
    'this',
    'throw',
    'try',
    'typeof',
    'var',
    'void',
    'while',
    'with',
    'yield',
    'true',
    'false',
    'null',
    'undefined',
  ];
  return keywords.includes(word.split('.')[0]);
};

// 检查是否是内置对象或方法调用
const isBuiltinObject = (word: string) => {
  const builtins = [
    'console',
    'Math',
    'Date',
    'Array',
    'Object',
    'String',
    'Number',
    'Boolean',
    'RegExp',
    'JSON',
    'parseInt',
    'parseFloat',
    'isNaN',
    'isFinite',
    'Utils',
    'window',
    'document',
  ];

  // 检查是否是内置对象
  const rootObject = word.split('.')[0];
  if (builtins.includes(rootObject)) {
    return true;
  }

  // 检查是否是常见的数组/对象方法调用
  const commonMethods = [
    'forEach',
    'map',
    'filter',
    'reduce',
    'find',
    'findIndex',
    'some',
    'every',
    'push',
    'pop',
    'shift',
    'unshift',
    'slice',
    'splice',
    'join',
    'concat',
    'indexOf',
    'lastIndexOf',
    'includes',
    'sort',
    'reverse',
    'length',
    'toString',
    'valueOf',
    'hasOwnProperty',
    'propertyIsEnumerable',
  ];

  // 如果整个word是方法名，则排除
  if (commonMethods.includes(word)) {
    return true;
  }

  // 如果是 xxx.method 格式且method是常见方法，则不排除（因为xxx可能是变量）
  return false;
};

// 根据路径查找变量定义
const findVariableByPath = (varPath: string) => {
  console.log('查找变量路径:', varPath);

  // 处理_data前缀的局部变量
  if (varPath.startsWith('_data.')) {
    const localPath = varPath.substring(6); // 移除'_data.'前缀
    return findVariableInList(props.localVariables, localPath);
  }

  // 查找临时变量
  return findVariableInList(props.currentVariables, varPath);
};

// 在变量列表中查找具体路径的变量
const findVariableInList = (variables: any[], targetPath: string) => {
  console.log('在变量列表中查找:', { targetPath, variableCount: variables.length });

  // 首先尝试精确匹配（使用key字段）
  let exactMatch = variables.find((v) => v.key === targetPath);
  if (exactMatch) {
    console.log('找到精确匹配:', exactMatch);
    return exactMatch;
  }

  // 如果没有精确匹配，尝试在嵌套结构中查找
  for (const variable of variables) {
    if (variable.key && targetPath.startsWith(variable.key + '.')) {
      // 这是一个嵌套路径，需要在children中查找
      const remainingPath = targetPath.substring(variable.key.length + 1);
      const nestedVariable = findNestedVariable(variable, remainingPath);
      if (nestedVariable) {
        console.log('找到嵌套变量:', nestedVariable);
        return nestedVariable;
      }
    }
  }

  // 如果还是没找到，返回最接近的父级变量
  const parentMatch = variables.find((v) => v.key && targetPath.startsWith(v.key + '.'));
  console.log('返回父级匹配:', parentMatch);
  return parentMatch;
};

// 在变量的children中递归查找
const findNestedVariable = (parentVariable: any, remainingPath: string): any => {
  if (!parentVariable.children || !Array.isArray(parentVariable.children)) {
    return null;
  }

  const pathParts = remainingPath.split('.');
  const currentKey = pathParts[0];
  const restPath = pathParts.slice(1).join('.');

  for (const child of parentVariable.children) {
    if (child.key === currentKey) {
      if (restPath) {
        // 还有更深的路径，继续递归
        return findNestedVariable(child, restPath);
      } else {
        // 找到了目标字段
        return child;
      }
    }
  }

  return null;
};

// 移除路径中的ROOT节点
const removeRootFromPath = (varPath: string): string => {
  // 如果路径包含.ROOT.，则移除ROOT部分
  if (varPath.includes('.ROOT.')) {
    return varPath.replace(/\.ROOT\./g, '.');
  }
  // 如果路径以ROOT.开头，则移除ROOT.前缀
  if (varPath.startsWith('ROOT.')) {
    return varPath.substring(5); // 移除'ROOT.'
  }
  // 如果路径就是ROOT，返回空字符串（这种情况下应该直接使用值）
  if (varPath === 'ROOT') {
    return '';
  }
  return varPath;
};

// 移除值中的ROOT节点包装
const removeRootFromValue = (sampleValue: any, variable: any): any => {
  // 如果变量有ROOT子节点，则提取ROOT的内容
  if (variable && variable.children) {
    const rootChild = variable.children.find((child: any) => child.key === 'ROOT');
    if (rootChild) {
      console.log('发现ROOT子节点，提取其内容:', rootChild);
      // 如果ROOT节点有自己的children，则生成这些children的值
      if (rootChild.children && rootChild.children.length > 0) {
        const rootContent = {};
        rootChild.children.forEach((child: any) => {
          if (child.key) {
            rootContent[child.key] = getSampleValue(child.type, child.children, true);
          }
        });
        return rootContent;
      }
      // 如果ROOT节点没有children，返回根据其类型生成的值
      return getSampleValue(rootChild.type, undefined, true);
    }
  }
  return sampleValue;
};

// 在对象中设置嵌套值
const setNestedValue = (obj: any, path: string, value: any) => {
  console.log('设置嵌套值:', { path, value });

  // 如果路径为空，说明是ROOT节点的情况，直接合并到根对象
  if (!path || path === '') {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      Object.assign(obj, value);
      console.log('直接合并ROOT内容到根对象:', obj);
      return;
    }
  }

  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }

  const finalKey = keys[keys.length - 1];
  current[finalKey] = value;
  console.log('设置完成:', { finalKey, value, currentObject: current });
};

// 获取示例值
const getSampleValue = (type: string, children?: any[], isInitial: boolean = false) => {
  console.log('获取示例值，类型:', type, '子字段:', children?.length || 0, '是否初始化:', isInitial);

  // 如果没有类型或类型为空，默认为字符串
  if (!type) {
    console.log('类型为空，默认返回字符串');
    return 'value';
  }

  type = type.toLowerCase().trim();

  switch (type) {
    case 'string':
    case 'text':
    case 'varchar':
    case 'char':
    case 'nvarchar':
    case 'nchar':
      return 'value';
    case 'number':
    case 'int':
    case 'integer':
    case 'decimal':
    case 'float':
    case 'double':
    case 'numeric':
      return 123;
    case 'boolean':
    case 'bool':
    case 'bit':
      return true;
    case 'array':
    case 'list':
      // 如果有子字段定义，生成包含子字段的对象数组
      if (children && children.length > 0) {
        const sampleItem = {};
        children.forEach((child) => {
          if (child.key) {
            sampleItem[child.key] = getSampleValue(child.type, child.children, isInitial);
          }
        });
        return [sampleItem]; // 返回包含一个示例对象的数组
      }
      return [1, 2, 3]; // 默认简单数组
    case 'object':
    case 'json':
    case 'dict':
    case 'dictionary':
      // 如果有子字段定义，生成包含子字段的对象
      if (children && children.length > 0) {
        const sampleObject = {};
        children.forEach((child) => {
          if (child.key) {
            sampleObject[child.key] = getSampleValue(child.type, child.children, isInitial);
          }
        });
        return sampleObject;
      }
      // 初始化时返回样本对象，刷新时返回空对象
      return isInitial ? { key: 'value' } : {};
    case 'date':
      return dayjs().format('YYYY-MM-DD');
    case 'datetime':
    case 'timestamp':
      return dayjs().format('YYYY-MM-DD HH:mm:ss');
    case 'time':
      return dayjs().format('HH:mm:ss');
    default:
      // 对于未知类型，检查是否有子字段来判断是否为对象类型
      if (children && children.length > 0) {
        console.log('未知类型但有子字段，按对象处理:', type);
        const sampleObject = {};
        children.forEach((child) => {
          if (child.key) {
            sampleObject[child.key] = getSampleValue(child.type, child.children, isInitial);
          }
        });
        return sampleObject;
      }
      // 对于未知类型，默认返回字符串
      console.log('未知类型，默认返回字符串:', type);
      return 'value';
  }
};

// 执行脚本测试
const executeScriptTest = async () => {
  if (!scriptTestCode.value?.trim()) {
    MessagePlugin.warning('请先输入测试脚本内容');
    return;
  }

  scriptTestLoading.value = true;
  try {
    const response = await api.run(Services.scriptTest, {
      script: scriptTestCode.value,
      inputData: JSON.stringify(scriptTestInputData.value),
    });

    scriptTestResult.value = response;

    if (response.success !== false) {
      MessagePlugin.success('脚本测试执行成功');
    } else {
      MessagePlugin.error(`脚本测试失败: ${response.errorMessage || '未知错误'}`);
    }
  } catch (error) {
    console.error('脚本测试失败:', error);
    scriptTestResult.value = {
      success: false,
      errorMessage: error.message || '测试执行失败',
    };
    MessagePlugin.error(`脚本测试失败: ${error.message}`);
  } finally {
    scriptTestLoading.value = false;
  }
};

// 保存脚本到外部编辑器
const saveScriptToEditor = () => {
  if (!scriptTestCode.value?.trim()) {
    MessagePlugin.warning('测试脚本内容为空');
    return;
  }

  // 将测试脚本代码同步到外部编辑器
  scriptValue.value = scriptTestCode.value;
  MessagePlugin.success('脚本已保存到编辑器');

  scriptTestVisible.value = false;
};

// 合并输入参数，保留已有值
const mergeInputParameters = (previousData: any) => {
  const newData = scriptTestInputData.value;

  // 检查值是否为用户填写的值（非默认值）
  const isUserFilledValue = (value: any) => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value !== '' && value !== 'sample text';
    if (typeof value === 'number') return value !== 0 && value !== 123; // 123是默认的数字示例值
    if (typeof value === 'boolean') return true;
    if (Array.isArray(value)) {
      // 检查是否为默认数组 [1, 2, 3]
      if (value.length === 3 && value[0] === 1 && value[1] === 2 && value[2] === 3) return false;
      return value.length > 0;
    }
    if (typeof value === 'object') {
      const keys = Object.keys(value);
      if (keys.length === 0) return false; // 空对象不是用户填写的
      // 检查是否为默认对象 { key: 'value' }
      if (keys.length === 1 && keys[0] === 'key' && value.key === 'value') return false;
      return true;
    }
    return true;
  };

  // 递归合并对象，优先保留用户填写的值
  const deepMerge = (newObj: any, oldObj: any): any => {
    const result = { ...newObj };

    for (const key in oldObj) {
      if (oldObj.hasOwnProperty(key)) {
        if (result.hasOwnProperty(key)) {
          // 如果新旧都有这个字段
          if (
            typeof oldObj[key] === 'object' &&
            oldObj[key] !== null &&
            typeof result[key] === 'object' &&
            result[key] !== null &&
            !Array.isArray(oldObj[key]) &&
            !Array.isArray(result[key])
          ) {
            // 如果都是对象，递归合并
            result[key] = deepMerge(result[key], oldObj[key]);
          } else {
            // 如果旧值是用户填写的，保留旧值
            if (isUserFilledValue(oldObj[key])) {
              result[key] = oldObj[key];
            } else if (!isUserFilledValue(result[key]) && isUserFilledValue(oldObj[key])) {
              // 如果新值是默认值，但旧值是用户填写的，保留旧值
              result[key] = oldObj[key];
            }
            // 否则使用新值（默认值）
          }
        } else {
          // 如果新数据中没有这个字段，但旧数据中有用户填写的值，则保留
          // 但要确保这个字段在脚本中仍然存在
          if (isUserFilledValue(oldObj[key])) {
            result[key] = oldObj[key];
          }
        }
      }
    }

    return result;
  };

  const mergedData = deepMerge(newData, previousData);
  scriptTestInputData.value = mergedData;
};

// 手动刷新输入参数
const refreshInputParameters = async () => {
  refreshingParams.value = true;
  try {
    // 保存当前的输入数据（深拷贝）
    const currentInputData = JSON.parse(JSON.stringify(scriptTestInputData.value));

    // 提取新的脚本参数（刷新模式）
    extractScriptParameters(false);

    // 合并保留已有的值
    mergeInputParameters(currentInputData);

    console.log('参数刷新完成，已保留用户填写的值');
  } finally {
    refreshingParams.value = false;
  }
};

// 监听测试对话框显示状态，重置相关状态
watch(scriptTestVisible, (val) => {
  if (val) {
    // 重置脚本测试相关状态
    scriptTestResult.value = null;
    scriptTestLoading.value = false;
    scriptTestInputData.value = {};
    // 确保测试脚本代码与当前编辑器内容同步
    scriptTestCode.value = scriptValue.value || '';
    refreshingParams.value = false;

    // 自动提取脚本参数
    nextTick(() => {
      extractScriptParameters(true);
    });
  }
});

// 暴露编辑器方法
const insertText = (text: string) => {
  editorRef.value?.insertText(text);
};

const formatCode = () => {
  editorRef.value?.formatCode();
};

defineExpose({
  insertText,
  formatCode,
  editorRef,
});
</script>

<style lang="less" scoped>
.script-editor-container {
  position: relative;
}

.floating-test-button {
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 10;

  .t-button {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.script-test-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
}

.test-script-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.script-editor {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  flex: 1;
  min-height: 180px;
}

.test-content-wrapper {
  display: flex;
  flex: 1;
  gap: 16px;
  min-height: 250px; /* 设置最小高度，允许子元素收缩 */
}

.test-input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许收缩 */
}

.test-result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许收缩 */
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
  flex-shrink: 0; /* 标题不收缩 */
}

.input-editor {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  flex: 1;
  min-height: 200px;
}

.test-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding: 8px 0;
  border-top: 1px solid var(--td-border-level-1-color);
  flex-shrink: 0; /* 按钮区域不收缩 */
}

.result-content {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 12px;
  background-color: var(--td-bg-color-container);
  flex: 1;
  overflow: auto;
}

.no-result-placeholder {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 12px;
  background-color: var(--td-bg-color-container);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.placeholder-text {
  color: var(--td-text-color-placeholder);
  font-size: 14px;
}

.success-result {
  .result-label {
    color: var(--td-success-color);
    font-weight: 500;
    margin-bottom: 8px;
  }
}

.error-result {
  .result-label {
    color: var(--td-error-color);
    font-weight: 500;
    margin-bottom: 8px;
  }

  .error-message {
    background-color: var(--td-error-color-1);
    border: 1px solid var(--td-error-color-3);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    color: var(--td-error-color);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.execution-time {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}
</style>

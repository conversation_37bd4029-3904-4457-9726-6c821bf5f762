import{d as y,D as a,aa as d,ab as O,ac as m}from"./index-Di7bhak2.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2H7.07713C9.24103 2 11 3.75897 11 5.92277V11H5.92287C3.75893 11 2 9.24099 2 7.07705V2ZM13 5.92277C13 3.75897 14.759 2 16.9229 2H22V7.07705C22 9.24099 20.2411 11 18.0771 11H13V5.92277ZM2 16.9229C2 14.759 3.75893 13 5.92287 13H11V18.0772C11 20.241 9.24103 22 7.07713 22H2V16.9229ZM13 13H18.0771C20.2411 13 22 14.759 22 16.9229V22H16.9229C14.759 22 13 20.241 13 18.0772V13Z"}}]},g=y({name:"System3FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-system-3-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(C,f.value)}});export{g as default};

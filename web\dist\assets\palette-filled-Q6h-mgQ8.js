import{d as v,D as a,aa as d,ab as O,ac as y}from"./index-Di7bhak2.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 1C5.92487 1 1 5.92487 1 12C1 18.042 5.54478 22.8971 11.6525 22.8971C12.183 22.8971 12.7172 22.8217 13.1424 22.5468C13.6567 22.2143 13.8564 21.697 13.8419 21.209C13.8297 20.8004 13.6721 20.4347 13.5521 20.1961C13.4359 19.9652 13.2963 19.7438 13.2 19.5911L13.1731 19.5483C12.9286 19.1594 12.7989 18.743 12.8227 18.3628C12.8444 18.0158 12.9984 17.596 13.5249 17.1555C13.9295 16.8169 14.463 16.7376 15.3798 16.7739C15.5177 16.7794 15.6676 16.7882 15.8269 16.7976C16.603 16.8433 17.6033 16.9022 18.535 16.6508C21.0902 15.9613 22.8253 14.0956 22.9977 11.5309C23.4176 5.28585 17.931 1 12 1ZM9.75 7.00391C9.75 5.89934 10.6454 5.00391 11.75 5.00391C12.8546 5.00391 13.75 5.89934 13.75 7.00391C13.75 8.10848 12.8546 9.00391 11.75 9.00391C10.6454 9.00391 9.75 8.10848 9.75 7.00391ZM5.0293 10.0039C5.0293 8.89934 5.92473 8.00391 7.0293 8.00391C8.13387 8.00391 9.0293 8.89934 9.0293 10.0039C9.0293 11.1085 8.13387 12.0039 7.0293 12.0039C5.92473 12.0039 5.0293 11.1085 5.0293 10.0039ZM16.5176 8.00391C17.6221 8.00391 18.5176 8.89934 18.5176 10.0039C18.5176 11.1085 17.6221 12.0039 16.5176 12.0039C15.413 12.0039 14.5176 11.1085 14.5176 10.0039C14.5176 8.89934 15.413 8.00391 16.5176 8.00391Z"}}]},g=v({name:"PaletteFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),C=a(()=>["t-icon","t-icon-palette-filled",l.value]),p=a(()=>s(s({},c.value),t.style)),u=a(()=>({class:C.value,style:p.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>O(m,u.value)}});export{g as default};

import{d,D as a,aa as O,ab as y,ac as C}from"./index-Di7bhak2.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){C(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23ZM15.5408 9.90565L17.3618 8.88133L16.3813 7.13818L13 9.04019L13 10.5L16.2 12.9L17.4 11.3L15.5408 9.90565ZM9 12V8.00002H7V12H9ZM9.40076 14.4993L8.90004 13.6337L7.16882 14.6351L7.66955 15.5007C8.53256 16.9926 10.1481 18 12 18C13.852 18 15.4675 16.9926 16.3305 15.5007L16.8312 14.6351L15.1 13.6337L14.5993 14.4993C14.0791 15.3986 13.1092 16 12 16C10.8909 16 9.92099 15.3986 9.40076 14.4993Z"}}]},b=d({name:"WinkFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-wink-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>y(m,f.value)}});export{b as default};
